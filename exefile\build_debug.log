*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'C:\Keil_v5\ARM\ARMCC5_06u7\Bin'
Rebuild target 'UpdataApp_S132_Debug'
compiling Common.c...
compiling ProductInfo.c...
compiling CalibCheckService_c.c...
compiling ble_bas_c.c...
compiling BlobService_c.c...
compiling ble_dis_c.c...
compiling ImuCalibService_c.c...
compiling ImuNorService_c.c...
compiling ble_srv_common.c...
compiling SysCfgService_c.c...
compiling ble_advertising.c...
compiling ble_advdata.c...
compiling TlsrUartService_c.c...
compiling ble_conn_state.c...
compiling BspInitDrv.c...
compiling SysStartUpTaskDrv.c...
compiling main.c...
compiling EvtProcessTaskDrv.c...
source\EvtProcessTask\EvtProcessTaskDrv.c(100): warning:  #177-D: variable "deviceInfo_t"  was declared but never referenced
      CtrlInfoData_str            deviceInfo_t;
source\EvtProcessTask\EvtProcessTaskDrv.c(43): warning:  #177-D: function "DeviceInfoCharRead"  was declared but never referenced
  static uint32_t DeviceInfoCharRead(ControllerSt_str * apController,uint16_t uuid,ble_dis_c_char_type_t  charType,CtrlInfoData_str *apInfoOut_t)
source\EvtProcessTask\EvtProcessTaskDrv.c: 2 warnings, 0 errors
compiling nrf_ble_gatt.c...
compiling nrfx_power.c...
compiling nrf_drv_uart.c...
compiling ble_db_discovery.c...
compiling nrf_ble_gq.c...
compiling nrf_drv_clock.c...
compiling BleClientDrv.c...
compiling nrfx_clock.c...
compiling time_sync.c...
source\time_sync_tx\time_sync.h(53): warning:  #47-D: incompatible redefinition of macro "NRF_LOG_LEVEL"  (declared at line 71 of ".\nRF5_SDK\components\libraries\log\nrf_log.h")
  #define NRF_LOG_LEVEL                      4
source\time_sync_tx\time_sync.c(1111): warning:  #175-D: subscript out of range
  	DEBUG_PRINTF("time Syn rf addr =%02x %02x %02x %02x %02x", ts_params.rf_addr[0], ts_params.rf_addr[1], ts_params.rf_addr[2], ts_params.rf_addr[3], ts_params.rf_addr[4], ts_params.rf_addr[5]);
source\time_sync_tx\time_sync.c(232): warning:  #177-D: function "config_ppi_on_rf_rx"  was declared but never referenced
  static inline void config_ppi_on_rf_rx(ts_params_t* p_param)
source\time_sync_tx\time_sync.c(319): warning:  #177-D: function "config_rf_rx_start"  was declared but never referenced
  static inline void config_rf_rx_start(ts_params_t* p_param)
source\time_sync_tx\time_sync.c(374): warning:  #177-D: function "get_rf_remote_stamp"  was declared but never referenced
  static inline uint64_t get_rf_remote_stamp(uint64_t* p_remote_raw)
source\time_sync_tx\time_sync.c(495): warning:  #177-D: function "linear_fit_get_result"  was declared but never referenced
  static void linear_fit_get_result(linear_fit_buffer_t* p_buffer, linear_fit_result_t* p_result)
source\time_sync_tx\time_sync.c(767): warning:  #177-D: function "get_local_rf_time_raw"  was declared but never referenced
  static inline uint64_t get_local_rf_time_raw(ts_params_t* p_param)
source\time_sync_tx\time_sync.c(775): warning:  #177-D: function "timestamp_push_data"  was declared but never referenced
  static inline void timestamp_push_data(ts_params_t* p_param, linear_fit_buffer_t* p_buffer, uint64_t* p_remote, uint64_t* p_local)
source\time_sync_tx\time_sync.c(786): warning:  #177-D: function "GetParamAccuracyUs"  was declared but never referenced
  static inline double GetParamAccuracyUs(ts_params_t* p_param, uint64_t* p_remote, uint64_t* p_local)
source\time_sync_tx\time_sync.c: 9 warnings, 0 errors
compiling UartRWDrv.c...
source\UartRWDrv\UartRWDrv.c(142): warning:  #223-D: function "app_uart_flush_Rx" declared implicitly
              app_uart_flush_Rx();
source\UartRWDrv\UartRWDrv.c(227): warning:  #223-D: function "app_uart_flush_Rx" declared implicitly
              app_uart_flush_Rx();
source\UartRWDrv\UartRWDrv.c(289): warning:  #223-D: function "app_uart_flush_Tx" declared implicitly
              app_uart_flush_Tx();
source\UartRWDrv\UartRWDrv.c: 3 warnings, 0 errors
compiling nrfx_prs.c...
compiling nrfx_gpiote.c...
compiling RxMsgProcessTaskDrv.c...
compiling BleHostCtrlDrv.c...
compiling app_error.c...
compiling app_error_handler_keil.c...
compiling nrfx_spi.c...
compiling nrf_queue.c...
compiling nrfx_uarte.c...
compiling app_fifo.c...
compiling nrfx_wdt.c...
compiling nrf_drv_spi.c...
compiling app_error_weak.c...
compiling nrfx_spim.c...
compiling app_scheduler.c...
compiling nrfx_uart.c...
compiling app_uart_fifo.c...
compiling app_timer.c...
compiling app_util_platform.c...
compiling hardfault_implementation.c...
compiling nrf_assert.c...
compiling nrf_atfifo.c...
compiling nrf_atflags.c...
compiling nrf_section_iter.c...
compiling nrf_fprintf.c...
compiling nrf_atomic.c...
compiling nrf_strerror.c...
compiling retarget.c...
compiling nrf_fprintf_format.c...
compiling nrf_balloc.c...
compiling nrf_fstorage.c...
compiling nrf_fstorage_sd.c...
compiling nrf_memobj.c...
compiling nrf_ringbuf.c...
compiling nrf_pwr_mgmt.c...
compiling SEGGER_RTT_Syscalls_KEIL.c...
compiling nrf_log_backend_rtt.c...
assembling arm_startup_nrf52.s...
compiling nrf_log_default_backends.c...
compiling nrf_log_backend_serial.c...
compiling SEGGER_RTT_printf.c...
compiling nrf_log_frontend.c...
compiling SEGGER_RTT.c...
compiling nrf_log_str_formatter.c...
compiling system_nrf52.c...
compiling nrf_sdh.c...
compiling nrf_sdh_soc.c...
compiling nrf_sdh_ble.c...
compiling SerialLedSingleton.c...
compiling SerialLedDrv.c...
source\SerialLedDrv\SerialLedDrv.c(194): warning:  #177-D: variable "reset_buf"  was declared but never referenced
  	const uint8_t reset_buf[280] = {0x00};
source\SerialLedDrv\SerialLedDrv.c: 1 warning, 0 errors
linking...
Program Size: Code=47552 RO-data=6008 RW-data=9388 ZI-data=24092  
FromELF: creating hex file...
After Build - User command #1: .\BootMergeBin\AllHexMergeGenerate_Debug.bat
Parsing input hex files.
Merging files.
Storing merged file.
Parsing input hex files.
Merging files.
Storing merged file.
".\Objects\AGIA0000-D0M-BleHost_App_float_Debug.axf" - 0 Error(s), 15 Warning(s).
Build Time Elapsed:  00:00:16
